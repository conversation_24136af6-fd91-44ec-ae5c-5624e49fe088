// renderer.js
const socketManager = (function() {
  let _socket = null;
  const _pendingEmits = [];
  let _isInitialized = false;

  return {
    init(token) {
      console.log("Token i am re ",token)
      if (_socket) return;

      console.log("🔌 Initializing socket connection...", token);
      _socket = io("http://localhost:10000", {
        transports: ['websocket'],
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        auth: { token }
      });

      _socket.on("connect", () => {
        console.log("✅ Desktop socket connected");
        _isInitialized = true;
        this._processPendingEmits();
      });

      _socket.on("disconnect", () => {
        console.log("❌ Desktop socket disconnected");
        _isInitialized = false;
      });

      _socket.on("connect_error", (error) => {
        console.log("🔥 Socket connection error:", error);
        _isInitialized = false;
      });

      // Forward all socket events to the existing handlers
      _socket.on("timer-update", (data) => {
        console.log("📨 Received timer update:", data);

      if (data) {
        // Always update state regardless of user
        runningTaskId = data.taskId;
        timerSeconds = data.seconds;
        isRunning = data.isRunning;

        // Update timer state
        updateTimerState();
      } else {
        console.log("⏹️ Stopping timer from socket update");
        runningTaskId = null;
        timerSeconds = 0;
        isRunning = false;
        updateTimerState();
      }

      // Force immediate UI update
      setTimeout(() => {
        updateButtonStates();
        updateTimerDisplay();
      }, 50);
      });

      _socket.on("task-data-update", () => {
          console.log("📊 Task data updated, refreshing...");
        setTimeout(() => {
          window.electronAPI.getUpdatedTasks();
        }, 100);
      });

 -
      _socket.on("button-state-update", (data) => {
            console.log("🔘 Button state update:", data);
          if (data.taskId) {
            runningTaskId = data.taskId;
            isRunning = data.isRunning;
          } else {
            runningTaskId = null;
            isRunning = false;
          }
          setTimeout(() => updateButtonStates(), 50);
      });

      _socket.on("break-in-update", (data) => {
        window.electronAPI.socketBreakIn();
      });

      _socket.on("break-out-update", (data) => {
        window.electronAPI.socketBreakOut();
      });

      _socket.on("check-out-update", (data) => {
        window.electronAPI.socketCheckOut();
      });

      return _socket;
    },

    emitLateCheckInDescription(data) {
      if (_isInitialized && _socket && _socket.connected) {
        _socket.emit('late-checkin-description', data);
        console.log("✅ Late check-in description emitted:", data);
        return true;
      } else {
        console.warn("⚠️ Socket not ready, queueing late check-in description");
        _pendingEmits.push({ event: 'late-checkin-description', data });
        return false;
      }
    },


    emit(event, data) {
      if (_isInitialized && _socket?.connected) {
        _socket.emit(event, data);
      } else {
        console.warn(`Queueing emit (socket not ready): ${event}`, data);
        _pendingEmits.push({ event, data });
      }
    },

    _processPendingEmits() {
      while (_pendingEmits.length > 0) {
        const { event, data } = _pendingEmits.shift();
        console.log(`Processing queued emit: ${event}`);
        this.emit(event, data);
      }
    },

    getSocket() {
      return _socket;
    },

    isConnected() {
      return _isInitialized && _socket?.connected;
    }
  };
})();

// Replace all socket.emit calls with socketManager.emit

// Desktop Timer System - Exact replica of Web Tasklist functionality
let socket = null;
let runningTaskId = null;
let timerSeconds = 0;
let isRunning = false;
let timerInterval = null;
let userID = null;
let authToken = null;
let products = [];

// Timer formatting - identical to web
const formatTimer = (seconds) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;
  return `${h.toString().padStart(2, "0")}:${m.toString().padStart(2, "0")}:${s
    .toString()
    .padStart(2, "0")}`;
};

// Timer tick function - fixed version
const startTimerTick = () => {
  // Clear existing interval first
  if (timerInterval) {
    clearInterval(timerInterval);
    timerInterval = null;
  }

  // Only start if we have a running task
  if (isRunning && runningTaskId) {
    console.log("🚀 Starting timer for task:", runningTaskId);
    timerInterval = setInterval(() => {
      timerSeconds++;
      console.log("⏰ Timer tick:", timerSeconds);

      // Update UI immediately
      updateTimerDisplay();

      // Update UI immediately on each tick
      updateTimerDisplay();

      // Emit to web every 5 seconds to reduce load
      if (timerSeconds % 5 === 0 && socketManager && socketManager.isConnected()) {
        socketManager.emit("timer-sync", {
          taskId: runningTaskId,
          seconds: timerSeconds,
          isRunning: true,
          userId: userID,
        });
      }
    }, 1000);
  } else {
    console.log("⏹️ Timer stopped");
  }
};

// Update timer display in UI
const updateTimerDisplay = () => {
  const formattedTime = formatTimer(timerSeconds);

  // Update all timer cells
  const allTimerCells = document.querySelectorAll('[id^="timer-"]');
  allTimerCells.forEach((cell) => {
    const taskId = cell.id.replace("timer-", "");
    if (taskId === runningTaskId) {
      cell.textContent = formattedTime;
      cell.style.fontWeight = "bold";
      cell.style.color = "#007bff";
    } else {
      cell.style.fontWeight = "normal";
      cell.style.color = "inherit";
    }
  });
};

// Update button states immediately
const updateButtonStates = () => {
  const allRows = document.querySelectorAll("#user-table tr");

  allRows.forEach((row) => {
    const actionCell = row.children[2]; // Actions column
    if (!actionCell) return;

    const playBtn = row.querySelector(".play-btn");
    const pauseBtn = row.querySelector(".pause-btn");
    const stopBtn = row.querySelector(".submit-btn");

    if (playBtn || pauseBtn || stopBtn) {
      const taskId =
        playBtn?.getAttribute("data-taskid") ||
        pauseBtn?.getAttribute("data-taskid") ||
        stopBtn?.getAttribute("data-taskid");
      const isCurrentTask = runningTaskId === taskId;
      const showPause = isCurrentTask && isRunning;
      const showPlay = !runningTaskId || (isCurrentTask && !isRunning);

      // Create buttons if they don't exist
      if (showPlay && !playBtn) {
        const newPlayBtn = document.createElement("img");
        newPlayBtn.src = "../assets/Play.png";
        newPlayBtn.className = "play-btn";
        newPlayBtn.setAttribute("data-taskid", taskId);
        newPlayBtn.alt = "Play";
        newPlayBtn.width = 26;
        newPlayBtn.addEventListener("click", () => {
          const product = products.find((p) =>
            p.taskArr.some((t) => t._id === taskId)
          );
          if (product) handleStartTask(taskId, product._id);
        });
        actionCell.insertBefore(newPlayBtn, stopBtn);
      }

      if (showPause && !pauseBtn) {
        const newPauseBtn = document.createElement("img");
        newPauseBtn.src = "../assets/pause.png";
        newPauseBtn.className = "pause-btn";
        newPauseBtn.setAttribute("data-taskid", taskId);
        newPauseBtn.alt = "Pause";
        newPauseBtn.width = 26;
        newPauseBtn.addEventListener("click", () => {
          const product = product.find((p) =>
            p.taskArr.some((t) => t._id === taskId)
          );
          if (product) handlePauseTask(taskId, product._id);
        });
        actionCell.insertBefore(newPauseBtn, stopBtn);
      }

      // Update button visibility
      if (playBtn) {
        playBtn.style.display = showPlay ? "inline" : "none";
      }

      if (pauseBtn) {
        pauseBtn.style.display = showPause ? "inline" : "none";
      }

      // Update stop button state
      if (stopBtn) {
        if (isCurrentTask) {
          stopBtn.style.opacity = "1";
          stopBtn.style.pointerEvents = "auto";
        } else {
          stopBtn.style.opacity = "0.5";
          stopBtn.style.pointerEvents = "none";
        }
      }
    }
  });
};

// Watch for timer state changes and start/stop timer accordingly
const updateTimerState = () => {
  console.log("🔄 Timer state update:", {
    isRunning,
    runningTaskId,
    timerSeconds,
  });
  
  // Fix inconsistent state: if no running task, ensure isRunning is false
  if (!runningTaskId) {
    isRunning = false;
    timerSeconds = 0;
  }
  
  startTimerTick();
};

// Utility functions
const totalTimeCalculationFun = (totalSeconds) => {
  if (typeof totalSeconds !== "number" || isNaN(totalSeconds)) {
    return "0h 0m 0s";
  }
  const hours = Math.floor(totalSeconds / 3600);
  const remainingMinutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);
  return `${hours}h ${remainingMinutes}m ${seconds}s`;
};


window.electronAPI.socketCheckInEmit(() => {
    
  socketManager.emit('check-in', { status: 'check-in' });
    
})

window.electronAPI.socketCheckOutEmit(() => {
    
  socketManager.emit('check-out', { status: 'check-out', fromDesktop: true });
    
})

window.electronAPI.socketBreakInEmit((data) => {
    
    socketManager.emit('break-in', data);
 
})

window.electronAPI.socketBreakOutEmit((data) => {
   
    socketManager.emit('break-out', data);
    
})

// Update the late check-in handler in desktop app
window.electronAPI.lateCheckInEmit((description) => {
  console.log("📝 Late check-in description from desktop:", description);
  
  // Emit to server/web app immediately with proper userId
  if (socketManager.isConnected() && userID) {
    const success = socketManager.emitLateCheckInDescription({
      description: description,
      userId: userID,
      timestamp: new Date().toISOString()
    });
    
    if (success) {
      console.log("✅ Late check-in emitted successfully");
    }
  } else {
    console.warn("⚠️ Cannot emit - socket not connected or userID missing", { connected: socketManager.isConnected(), userID });
  }
});


// Main application
window.electronAPI.productGetFun((data) => {
  console.log("📦 Received product data:", data);
  
  const { products: receivedProducts, userID: receivedUserID, todayActivity, authToken } = data;
  
  // Set global variables
  userID = receivedUserID;
  window.userID = receivedUserID; // Also set on window for debugging
  products = receivedProducts;
  let statusDropdownSelected = "taskStatus";
  let projectDropDownSelected = "search";
  let currentTaskBtn = "";
  let paginationCount = 0;

  console.log("🔧 Setting userID:", userID);
  socketManager.init(authToken);

  let currentPage = 1;
  const tasksPerPage = 10;
  let filteredTasks = [];

  // DOM elements
  const userTable = document.querySelector("#user-table");
  const comepleteTaskBtn = document.getElementById("completedBtn");
  const projectDropdown = document.getElementById("projectDropdown");
  const statusDropdown = document.getElementById("statusDropdown");
  // statusDropdown.innerHTML = "";
  projectDropdown.innerHTML = "";
  const projectDropdownOption = document.createElement("option");
  projectDropdownOption.innerHTML = "Search...";
  projectDropdownOption.value = "search";
  projectDropdown.appendChild(projectDropdownOption);
  const footerTask = document.getElementById("task-labe");
  const overdueTaskBtn = document.getElementById("overdueBtn");
  const todayTaskBtn = document.getElementById("todayBtn");
  const upcomingTaskBtn = document.getElementById("upcomingBtn");
  const clockIn = document.getElementById("clockIn");
  const clockOut = document.getElementById("clockOut");
  const timeAtWork = document.getElementById("timeAtWork");
  const productivity = document.getElementById("productiveTime");

  // Setup UI
  receivedProducts.forEach((product) => {
    const projectDropdownOption = document.createElement("option");
    projectDropdownOption.innerHTML = product.productName;
    projectDropdownOption.value = product.productName;

    projectDropdown.appendChild(projectDropdownOption);
  });
  projectDropdown.addEventListener("change", (e) => {
    filteredTasks = [];
    projectDropDownSelected = e.target.value;
    receivedProducts.forEach((product) => {
      
      product.taskArr.forEach((task) => {
   
            const isProjectMatch =
              projectDropDownSelected === product.productName;
            const isStatusNotDefault = statusDropdownSelected !== "taskStatus";
            const isTaskStatusMatch =
              task.taskStatus === statusDropdownSelected;
              
            if (isProjectMatch && (!isStatusNotDefault || isTaskStatusMatch)) {
              renderTasksDropdown({ task, product }, todayTaskBtn);
            }

        
      });
    });
  });

  statusDropdown.addEventListener("change", (e) => {
    filteredTasks = [];
    statusDropdownSelected = e.target.value;
    console.log("Status Dropdown Called ", statusDropdownSelected);
    receivedProducts.forEach((product) => {
      product.taskArr.forEach((task) => {
              const isProjectMatch =
              projectDropDownSelected === product.productName;
            const isProjectNotDefault = projectDropDownSelected !== "search";
            const isTaskStatusMatch =
              task.taskStatus === statusDropdownSelected;
              
            if (isTaskStatusMatch && (!isProjectNotDefault || isProjectMatch)) {
              renderTasksDropdown({ task, product }, todayTaskBtn);
            }
      });
    });
  });

  if (todayActivity) {
    clockIn.innerHTML = timeFormatter(todayActivity.checkInTime);
    clockOut.innerHTML = todayActivity.checkOutTime
      ? timeFormatter(todayActivity.checkOutTime)
      : "Online";
    timeAtWork.innerHTML = totalWorkingTimeCalculationFun(
      todayActivity.totalWorkingTime * 60
    );

    let productivityFilled = 0;
    if (todayActivity.totalWorkingTime !== 0) {
      for (let i = 0; i < todayActivity.productivityHistory.length; i++) {
        productivityFilled +=
          todayActivity.productivityHistory[i].productivityFilled;
      }
      productivity.innerHTML = `${(
        (productivityFilled / todayActivity.totalWorkingTime) *
        100
      ).toFixed(2)} %`;
    } else {
      productivity.innerHTML = "0.0%";
    }
  }

  window.electronAPI.getUpdateProgressFun((data) => {
    const { todayActivity } = data;
    console.log("Second todayactivity ", todayActivity);
    const clockIn = document.getElementById("clockIn");
    const clockOut = document.getElementById("clockOut");
    const timeAtWork = document.getElementById("timeAtWork");
    const productivity = document.getElementById("productiveTime");

    if (todayActivity) {
      clockIn.innerHTML = timeFormatter(todayActivity.checkInTime);
      clockOut.innerHTML = todayActivity.checkOutTime
        ? timeFormatter(todayActivity.checkOutTime)
        : "Online";
      timeAtWork.innerHTML = totalWorkingTimeCalculationFun(
        todayActivity.totalWorkingTime * 60
      );
      let productivityFilled = 0;

      if (todayActivity.totalWorkingTime !== 0) {
        for (let i = 0; i < todayActivity.productivityHistory.length; i++) {
          productivityFilled +=
            todayActivity.productivityHistory[i].productivityFilled;
        }
    
        productivity.innerHTML = `${(
          (productivityFilled / todayActivity.totalWorkingTime) *
          100
        ).toFixed(2)} %`;
      } else {
        productivity.innerHTML = "0.0%";
      }
    }
  });

  function renderTasksDropdown(data, activeTabBtn) {
    resetTabs(activeTabBtn);

    const { task, product } = data;

    filteredTasks.push({ task, product });
    dataCount = filteredTasks.length;
    currentPage = 1;

    renderPage(currentPage);
    createPagination();
  }

  function timeFormatter(data) {
    const date = new Date(data);
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    const formattedHour = hours % 12 || 12;
    const formattedMinute = minutes.toString().padStart(2, "0");
    return `${formattedHour}:${formattedMinute} ${ampm}`;
  }

  function totalWorkingTimeCalculationFun(totalSeconds) {
    const hours = Math.floor(totalSeconds / 3600);
    const remainingMinutes = Math.floor((totalSeconds % 3600) / 60);
    return `${hours}h ${remainingMinutes}m`;
  }

  function resetTabs(activeTab) {
    todayTaskBtn.className = "tab";
    comepleteTaskBtn.className = "tab";
    overdueTaskBtn.className = "tab";
    upcomingTaskBtn.className = "tab";
    activeTab.className = "tab active";
  }

  function renderPage(pageNumber) {
    if (!userTable) return;

    userTable.innerHTML = "";
    const start = (pageNumber - 1) * tasksPerPage;
    const end = pageNumber * tasksPerPage;
    const paginatedTasks = filteredTasks.slice(start, end);

    paginatedTasks.forEach(({ task, product }) => {
      const row = document.createElement("tr");
      const latestTask =
        products
          .find((p) => p._id === product._id)
          ?.taskArr.find((t) => t._id === task._id) || task;
      const isCurrentTask = runningTaskId === latestTask._id;
      const showPause = isCurrentTask && isRunning;
      const showPlay = !runningTaskId || (isCurrentTask && !isRunning);

      // Debug logging for button states
      if (isCurrentTask) {
        console.log(
          `🔘 Task ${latestTask._id}: running=${isRunning}, showPlay=${showPlay}, showPause=${showPause}`
        );
      }

      const hasAccess =
        latestTask.assignee?.includes(userID) ||
        product.visibility ||
        product.members?.includes(userID) ||
        latestTask.reporter === userID;

      if (!hasAccess) return;

      if ((latestTask.taskStatus || "").trim().toLowerCase() !== "completed") {
        row.innerHTML = `
        <td>${latestTask.taskTitle}</td>
        <td>${product.productName}</td>
        <td>
          ${
            latestTask.taskStatus === "Completed"
              ? `<img src="../assets/Right.png" alt="Completed" width="26" />`
              : `
                <img src="../assets/${
                  isCurrentTask && isRunning ? "pause" : "Play"
                }.png"
                  class="${
                    isCurrentTask && isRunning ? "pause-btn" : "play-btn"
                  }"
                  data-taskid="${latestTask._id}"
                  alt="${isCurrentTask && isRunning ? "Pause" : "Play"}"
                  width="26"
                  ${
                    isRunning &&
                    runningTaskId &&
                    runningTaskId !== latestTask._id
                      ? 'style="opacity:0.5;pointer-events:none;"'
                      : ""
                  } />
                <img src="../assets/Stop.png"
                  class="submit-btn"
                  data-taskid="${latestTask._id}"
                  alt="Submit"
                  width="26"
                  ${
                    !isCurrentTask
                      ? 'style="opacity:0.5;pointer-events:none;"'
                      : ""
                  } />
              `
          }
        </td>
        <td id="timer-${latestTask._id}">
          ${
            isCurrentTask
              ? formatTimer(timerSeconds)
              : totalTimeCalculationFun(latestTask.totalSpent * 3600)
          }
        </td>
        <td>${totalTimeCalculationFun(latestTask.totalSpent * 3600)}</td>
        <td>${totalTimeCalculationFun(latestTask.totalHours * 3600)}</td>
      `;

      // Add event listeners
      const playBtn = row.querySelector(".play-btn");
      const pauseBtn = row.querySelector(".pause-btn");
      const stopBtn = row.querySelector(".submit-btn");

        if (playBtn) {
          playBtn.addEventListener("click", () => {
            handleStartTask(latestTask._id, product._id);
          });
        }

        if (pauseBtn) {
          pauseBtn.addEventListener("click", () => {
            handlePauseTask(latestTask._id, product._id);
          });
        }

        if (stopBtn) {
          stopBtn.addEventListener("click", () =>
            handleStopTask(latestTask._id, product._id)
          );
        }
      } else {
        row.innerHTML = `
        <td>${latestTask.taskTitle}</td>
        <td>${product.productName}</td>
        <td>
          <img src="../assets/Right.png" alt="Completed" width="26" />
        </td>
        <td>${totalTimeCalculationFun(latestTask.totalSpent * 3600)}</td>
        <td>${totalTimeCalculationFun(latestTask.totalSpent * 3600)}</td>
        <td>${totalTimeCalculationFun(latestTask.totalHours * 3600)}</td>
      `;
      }

      userTable.appendChild(row);
    });

    if (footerTask) {
      footerTask.textContent = `Tasks: ${filteredTasks.length}`;
    }
  }

  function renderTasks(filterFn, activeTabBtn) {
    resetTabs(activeTabBtn);
    filteredTasks = [];

    products.forEach((product) => {
      if (product.taskArr && product.taskArr.length > 0) {
        product.taskArr.forEach((task) => {
          const hasAccess =
            task.assignee?.includes(userID) ||
            product.visibility ||
            product.members?.includes(userID) ||
            task.reporter === userID;

          if (hasAccess && filterFn(task)) {
            filteredTasks.push({ task, product });
          }
        });
      }
    });

    currentPage = 1;
    renderPage(currentPage);
  }

  // Task handlers - identical to web logic
  const handleStartTask = async (taskId, projectId) => {
    try {
      if (runningTaskId && runningTaskId !== taskId && isRunning) {
        alert("Stop current task first!");
        return;
      }

      // Start timer immediately
      runningTaskId = taskId;
      timerSeconds = 0;
      isRunning = true;

      const timerData = {
        taskId,
        seconds: 0,
        isRunning: true,
        userId: userID,
      };

      // Make API call to start task
      try {
        const today = new Date().toISOString().split("T")[0];
        await window.electronAPI.startTask({ taskId, projectId, date: today });
      } catch (apiError) {
        console.error("❌ Desktop start API error:", apiError);
      }

      // Emit to web with task start event
      if (socketManager && socketManager.isConnected()) {
        socketManager.emit("timer-sync", timerData);
        socketManager.emit("task-started", { taskId, projectId, userId: userID });
      }

      updateTimerState();
      setTimeout(() => {
        renderPage(currentPage);
        window.electronAPI.getUpdatedTasks();
      }, 100);
    } catch (error) {
      console.error("Desktop start error:", error);
    }
  };

  const handlePauseTask = async (taskId, projectId) => {
    try {
      if (runningTaskId !== taskId) return;

      // Pause timer
      isRunning = false;
      const elapsedTime = timerSeconds;
      timerSeconds = 0;

      const timerData = {
        taskId,
        seconds: 0,
        isRunning: false,
        userId: userID,
      };

      // Make API call to save elapsed time to database
      try {
        const today = new Date().toISOString().split("T")[0];
        await window.electronAPI.pauseTask({
          taskId,
          projectId,
          elapsedTime,
          pauseTime: new Date().toISOString(),
          date: today,
          startTime: new Date().toISOString()
        });
      } catch (apiError) {
        console.error("❌ Desktop pause API error:", apiError);
      }

      // Emit to web with task pause event
      if (socketManager && socketManager.isConnected()) {
        socketManager.emit("timer-sync", timerData);
        socketManager.emit("task-paused", {
          taskId,
          projectId,
          elapsedTime,
          userId: userID,
        });
      }

      updateTimerState();
      setTimeout(() => {
        renderPage(currentPage);
        window.electronAPI.getUpdatedTasks();
      }, 100);
    } catch (error) {
      console.error("Desktop pause error:", error);
    }
  };

  const handleStopTask = async (taskId, projectId) => {
    try {
      // Stop timer
      const elapsedTime = timerSeconds;
      runningTaskId = null;
      timerSeconds = 0;
      isRunning = false;

      // Make API call to stop task
      try {
        const today = new Date().toISOString().split("T")[0];
        await window.electronAPI.stopTask({ taskId, projectId, elapsedTime, date: today });
      } catch (apiError) {
        console.error("❌ Desktop stop API error:", apiError);
      }

      // Emit to web with task stop event
      if (socketManager && socketManager.isConnected()) {
        socketManager.emit("timer-sync", null);
        socketManager.emit("task-stopped", {
          taskId,
          projectId,
          elapsedTime,
          userId: userID,
        });
      }

      updateTimerState();
      setTimeout(() => {
        renderPage(currentPage);
        window.electronAPI.getUpdatedTasks();
      }, 100);
    } catch (error) {
      console.error("Desktop stop error:", error);
    }
  };

  function createPagination() {
    const existingPagination = document.getElementById("pagination");
    if (existingPagination) existingPagination.remove();

    const paginationDiv = document.createElement("div");
    paginationDiv.id = "pagination";
    paginationDiv.style.textAlign = "center";
    paginationDiv.style.margin = "20px 0";

    const paginationElement = document.createElement("div");
    paginationElement.style.display = "inline-block";
    paginationDiv.appendChild(paginationElement);

    paginationCount = Math.ceil(filteredTasks.length / tasksPerPage);

    const leftA = document.createElement("a");
    leftA.innerHTML = "&laquo;";
    leftA.style.cursor = "pointer";
    leftA.addEventListener("click", () => {
      if (currentPage > 1) {
        currentPage--;
        renderPage(currentPage);
        createPagination();
      }
    });
    paginationElement.appendChild(leftA);

    for (let i = 1; i <= paginationCount; i++) {
      const paginationBtn = document.createElement("a");
      paginationBtn.textContent = i;
      paginationBtn.style.margin = "0 5px";
      paginationBtn.style.cursor = "pointer";
      if (i === currentPage) paginationBtn.style.fontWeight = "bold";

      paginationBtn.addEventListener("click", () => {
        currentPage = i;
        renderPage(currentPage);
        createPagination();
      });
      paginationElement.appendChild(paginationBtn);
    }

    const rightA = document.createElement("a");
    rightA.innerHTML = "&raquo;";
    rightA.style.cursor = "pointer";
    rightA.addEventListener("click", () => {
      if (currentPage < paginationCount) {
        currentPage++;
        renderPage(currentPage);
        createPagination();
      }
    });
    paginationElement.appendChild(rightA);

    footer.parentNode.insertBefore(paginationDiv, footer);
  }

  // Tab filters
  todayTaskBtn.addEventListener("click", () => {
    projectDropdown.value = "search";
    statusDropdown.value = "taskStatus";
    currentTaskBtn = "today";
    renderTasks((task) => {
      const today = new Date().setHours(0, 0, 0, 0);
      const taskCreateDate = task.startDate
        ? new Date(task.startDate).setHours(0, 0, 0, 0)
        : new Date(task.createdAt).setHours(0, 0, 0, 0);
      const isCompleted = task.taskStatus === "Completed";
      return taskCreateDate === today && !isCompleted;
    }, todayTaskBtn);
  });

  overdueTaskBtn.addEventListener("click", () => {
    projectDropdown.value = "search";
    statusDropdown.value = "taskStatus";
    currentTaskBtn = "overdue";
    renderTasks((task) => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const taskEndDate = task.endDate
        ? new Date(task.endDate).setHours(0, 0, 0, 0)
        : new Date(task.createdAt).setHours(0, 0, 0, 0);
      const isCompleted = task.taskStatus === "Completed";
      console.log(
        " Overdue condition ",
        taskEndDate,
        today,
        taskEndDate < today,
        isCompleted
      );
      return taskEndDate < today && !isCompleted;
    }, overdueTaskBtn);
  });

  upcomingTaskBtn.addEventListener("click", () => {
    projectDropdown.value = "search";
    statusDropdown.value = "taskStatus";
    currentTaskBtn = "upcoming";
    renderTasks((task) => {
      const today = new Date().setHours(0, 0, 0, 0);

      const taskStartDate = task.startDate
        ? new Date(task.startDate).setHours(0, 0, 0, 0)
        : today;
      return (
        (taskStartDate && taskStartDate > today) || task.taskStatus === "To Do"
      );
    }, upcomingTaskBtn);
  });

  comepleteTaskBtn.addEventListener("click", () => {
    projectDropdown.value = "search";
    statusDropdown.value = "taskStatus";
    currentTaskBtn = "completed";
    renderTasks((task) => task.taskStatus === "Completed", comepleteTaskBtn);
  });

  todayTaskBtn.click();

  // Start timer state monitoring
  console.log("🎯 Initializing timer system...");
  updateTimerState();

  // Debug timer status every 10 seconds
  setInterval(() => {
    if (isRunning && runningTaskId) {
      console.log(
        `⏰ Timer active: ${formatTimer(
          timerSeconds
        )} for task ${runningTaskId}`
      );
    } else {
      console.log("⏸️ Timer inactive");
    }
  }, 10000);

  // Force UI update every second when timer is running
  setInterval(() => {
    if (isRunning && runningTaskId) {
      updateTimerDisplay();

      // Also update any timer displays directly
      const allTimerCells = document.querySelectorAll('[id^="timer-"]');
      allTimerCells.forEach((cell) => {
        if (cell.id === `timer-${runningTaskId}`) {
          cell.textContent = formatTimer(timerSeconds);
          cell.style.fontWeight = "bold";
          cell.style.color = "#007bff";
        }
      });
    }
  }, 1000);
});

// Cleanup
window.electronAPI.clearTaskDataFun(() => {
  runningTaskId = null;
  timerSeconds = 0;
  isRunning = false;

  if (timerInterval) {
    clearInterval(timerInterval);
    timerInterval = null;
  }

  if (socketManager && socketManager.isConnected()) {
    socketManager.emit("timer-sync", null);
  }
});

// window.electronAPI.startSocketFuncationlity((data) => {
//   console.log("Socket token ",data)
//     socketManager.init(data)
// });

document.addEventListener("DOMContentLoaded", () => {
  const reloadTaskBtn = document.getElementById("reloadTask");

  reloadTaskBtn.addEventListener("click", (e) => {
    window.electronAPI.getRecentTask();
  });
  window.electronAPI.getRecentTask();
});
