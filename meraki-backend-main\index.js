// Core dependencies
const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");
const http = require("http");
const connectDB = require('./config/connectDB');
const { Server } = require('socket.io');


// Load environment variables
dotenv.config();
const jwt = require("jsonwebtoken");
const secret = process.env.JWT_SECRET;

// Import models
const { db } = require('./models');
const ActivityModel = db.activity;

// Initialize Express application
const app = express();

// Create HTTP server from Express
const server = http.createServer(app);

const allowedOrigins = [
  'https://meraki-frontend-main-1qve.onrender.com',
  'http://localhost:3000' // Keep for local development
];

// CORS Configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS','PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  optionsSuccessStatus: 200
};

// Apply CORS middleware
app.use(cors(corsOptions));

// Handle preflight requests
app.options('*', cors(corsOptions));

let globalTimerState = null;

// Initialize Socket.IO
const io = new Server(server, {
  cors: {
    origin: allowedOrigins,
    methods: ['GET', 'POST', 'PUT','PATCH','DELETE'],
    credentials: true
  }
});

// Example using JWT token in query or headers
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  
  try {
    console.log("Before Token ",token,secret)
    const user = jwt.verify(token, secret); // Your token verification function
    console.log("After User ",user)
    socket.userId = user.id;
    next();
  } catch (err) {
    next(new Error('Authentication error'));
  }
});


// Setup Socket.IO events
io.on('connection', (socket) => {
  console.log('🔌 A user connected:', socket.id);
  const userRoom = `user:${socket.userId}`;
   socket.join(userRoom);
  
  
   // Send current timer state to newly connected client
  if (globalTimerState) {
    io.to(userRoom).emit('timer-update', globalTimerState);
  }

  socket.on('check-in', (data) => {
    console.log("Checked in");
    // socket.broadcast.emit('check-in-update', data);
     io.to(userRoom).emit('check-in-update', data);
  });

  socket.on('late-checkin-description', async (data) => {
  try {
    console.log("Received late check-in description:", data);
    
    // Update the activity record with the description
    await ActivityModel.updateOne(
      { 
        user: data.userId,
        checkInTime: {
          $gte: new Date(new Date().setHours(0, 0, 0, 0)),
          $lt: new Date(new Date().setHours(23, 59, 59, 999))
        }
      },
      { 
        $set: { 
          lateCheckInDiscription: data.description,
          lateCheckInTimestamp: data.timestamp
        } 
      }
    );

    // Broadcast to specific user room for faster delivery
    const targetUserRoom = `user:${data.userId}`;
    io.to(targetUserRoom).emit('late-checkin-description-update', {
      userId: data.userId,
      description: data.description,
      timestamp: data.timestamp
    });

    console.log("Late check-in description updated and broadcasted");

  } catch (error) {
    console.error("Error handling late check-in description:", error);
    socket.emit('error', { message: 'Failed to update late check-in description' });
  }
});
  
  socket.on("break-in", (data) => {
    
    // socket.broadcast.emit("break-in-update", data);
     io.to(userRoom).emit('break-in-update', data);
  });

  socket.on("break-out", (data) => {
    // socket.broadcast.emit("break-out-update", data);
     io.to(userRoom).emit('break-out-update', data);
  });

  socket.on("check-out", (data) => {
    // socket.broadcast.emit("check-out-update", data);
    io.to(userRoom).emit('check-out-update', data);
  });

  socket.on('timer-sync', (timerState) => {
    globalTimerState = timerState;
     console.log('Received timer-sync:', timerState);
   
    // Broadcast to all other clients
    // socket.broadcast.emit('timer-update', timerState);

     io.to(userRoom).emit('timer-update', timerState);
   
    // Also emit button state update
    // socket.broadcast.emit('button-state-update', {
    //   taskId: timerState?.taskId,
    //   isRunning: timerState?.isRunning,
    //   userId: timerState?.userId
    // });.

    io.to(userRoom).emit('button-state-update', {
      taskId: timerState?.taskId,
      isRunning: timerState?.isRunning,
      userId: timerState?.userId
    });
  });
 
  socket.on('task-started', (data) => {
    console.log('Task started:', data);
    // socket.broadcast.emit('task-started', data);
      io.to(userRoom).emit('task-started', data);
  });
 
  socket.on('task-paused', (data) => {
    console.log('Task paused:', data);
    // socket.broadcast.emit('task-paused', data);
    io.to(userRoom).emit('task-paused', data);
  });
 
  socket.on('task-stopped', (data) => {
    console.log('Task stopped:', data);
    // socket.broadcast.emit('task-stopped', data);
    io.to(userRoom).emit('task-stopped', data);
  });
 
  socket.on('task-data-update', () => {
    console.log('Task data update requested');
    // socket.broadcast.emit('task-data-update');
    io.to(userRoom).emit('task-data-update', data);
  });

  socket.on('disconnect', () => {
    socket.leave(userRoom);
  });
});

// Additional headers
app.use((req, res, next) => {
  const origin = req.headers.origin;
  if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  next();
});

// Body parsers
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Serve static files
app.use("/static", express.static(path.join(__dirname, '/static/screenshot')));

// Load Routes
require("./routes/auth.route")(app);

// Authentication middleware for protected routes
app.use("/api*", require('./middlewares/auth.middleware'));

// Modular Routes
require("./routes/user.route")(app);
require("./routes/department.route")(app);
require("./routes/designation.route")(app);
require("./routes/attendance.route")(app);
require("./routes/leave.route")(app);
require("./routes/expenses.route")(app);
require("./routes/activity.route")(app);
require('./routes/timeline.route')(app);
require('./routes/product.route')(app);
require('./routes/client.route')(app);
require('./routes/screenshot.route')(app);
require('./routes/sprint.route')(app);
require("./routes/setting.route")(app);
require('./routes/permissions.route')(app);

// Initialize scheduler
const scheduler = require('./services/scheduler.service');

// Connect to database
connectDB(process.env.MONGODB_URL);

// Start server
const port = process.env.PORT || 10000;
server.listen(port, () => {
  console.log(`✅ Backend server is running on port ${port}`);
  scheduler.initScheduler();
  console.log(`✅ Auto-checkout scheduler initialized`);
});