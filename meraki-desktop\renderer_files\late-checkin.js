document.addEventListener('DOMContentLoaded', () => {
  const lateInBtn = document.getElementById('lateInBtn');

  if (!lateInBtn) {
    console.error("lateInBtn element not found");
    return;
  }

  lateInBtn.addEventListener('click', async (e) => {
    e.preventDefault();

    const descriptionInput = document.getElementById('lateCheckIn');
    if (!descriptionInput) {
      console.error("lateCheckIn input not found");
      return;
    }

    const description = descriptionInput.value.trim();

    if (!description) {
      alert('Please provide a reason for late check-in');
      return;
    }

    try {
      // Call the Electron API to save the description
      await window.electronAPI.lateCheckInFun(description);

      // Close the modal/dialog
      const modal = document.querySelector('.late-checkin-modal');
      if (modal) {
        modal.style.display = 'none';
      }

      // Clear the input
      descriptionInput.value = '';

      console.log("Late check-in description submitted successfully");
    } catch (error) {
      console.error("Error submitting late check-in description:", error);
      alert('Failed to submit late check-in reason. Please try again.');
    }
  });
});
