
const {
  app,
  BrowserWindow,
  ipcMain,
  Menu,
  Tray,
  nativeImage,
  desktopCapturer,
  powerSaveBlocker,
  screen,
} = require("electron");
process.env.DEBUG = "sharp";
require("sharp");
const path = require("path");
const dotenvPath = path.join(__dirname, '.env');
require('dotenv').config({ path: dotenvPath });
const sharp = require("sharp");
const fs = require("fs");
const Store = require("electron-store");
const store = new Store();
const axios = require("axios");
const { GlobalKeyboardListener } = require("node-global-key-listener");


const FormData = require("form-data");
let keyCounts = 0;
const blockerId = powerSaveBlocker.start("prevent-app-suspension");
let tray = null;

// Windows variables
let loginWin = null;
let dashboardWin = null;
let aboutWin = null;
let updateWin = null;
let todayTaskWin = null;
let lateCheckInWin = null;
let earlyCheckOutWin = null;
let overTimeBreakWin = null;
let completedTaskWin = null;
let otherBreakWin = null;
let loginSuccessWin = null;

// Productivity variables
let idleTimeout = null;
let isIdle = false;
let stopTracking = false;
let isForceQuitting = false;
let isLoggingOut = false;
let products = [];

// Duration in ms (5 minutes)
const IDLE_LIMIT = 5 * 60 * 1000;

// MacOS menu setup
const menuItems = [
  {
    label: "Meraki",
    submenu: [
      { label: "About Xogo" },
      { type: "separator" },
      { label: "Services", submenu: [{ label: "Service Settings" }] },
      { label: "Hide Meraki Client" },
      { label: "Hide Others" },
      { label: "Show All" },
      { type: "separator" },
      { label: "Quit", role: "close" },
    ],
  },
];
const menu = Menu.buildFromTemplate(menuItems);
Menu.setApplicationMenu(menu);

// Initialize global key listener
const gkl = new GlobalKeyboardListener();

// Window creators
function createLoginWindow() {
  if (loginWin && !loginWin.isDestroyed()) {
    loginWin.show();
  } else {
    loginWin = new BrowserWindow({
      width: 500,
      height: 550,
      resizable: false,
      webPreferences: {
        preload: path.join(__dirname, "./preload_files/login.js"),
        contextIsolation: true,
        nodeIntegration: false,
      },
    });

    loginWin.loadFile("./html_files/login.html");
  }
}

function createLoginSuccessWin() {
  loginSuccessWin = new BrowserWindow({
    width: 350,
    height: 300,
    resizable: false,
    webPreferences: {
      preload: path.join(__dirname, "./preload_files/login-success.js"),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });

  loginSuccessWin.loadFile("./html_files/login-success.html");
}

async function createDashboardWindow() {
  if (dashboardWin && !dashboardWin.isDestroyed()) {
    dashboardWin.show();
  } else {
    dashboardWin = new BrowserWindow({
      width: 1000,
      height: 800,
      webPreferences: {
        preload: path.join(__dirname, "./preload_files/dashboard.js"),
        contextIsolation: true,
        nodeIntegration: false,
      },
    });
    // dashboardWin.webContents.openDevTools()
    dashboardWin.loadFile("./html_files/dashboard.html");
  }
  dashboardWin.webContents.openDevTools();

  dashboardWin.on("close", (event) => {
    event.preventDefault();
    dashboardWin.hide(); // Hide instead of closing
  });

  // Tray icon setup
  if (tray === null) {
    const iconPath = path.join(__dirname, "./assets/meraki-logo.png");
    const resizedIconBuffer = await sharp(iconPath)
      .resize(24, 24)
      .png()
      .toBuffer();
    const trayImage = nativeImage.createFromBuffer(resizedIconBuffer);

    tray = new Tray(trayImage);
    const contextMenu = dynamicMenu(false, true);

    tray.setToolTip("My Meraki");
    tray.setContextMenu(contextMenu);
  }

  console.log(" Dashboard existing ");

  let date = new Date();

  let user = store.get("user");
  let id = user._id;
  console.log(" User That i got ", process.env.API_URL);

  let result = await axios.get(
    `${process.env.API_URL}/today/history/${date}/${id}`,
    {
      headers: {
        Authorization: `Bearer ${store.get("authToken")}`,
      },
    }
  );

  store.set("todayActivity", result.data);

  if (store.get("todayActivity")) {
    getTaskDataFun();
  }
  if (result.data === null || result.data === undefined) {
    createTodayTaskWindow();
  } else {
    return;
  }
}

function createTodayTaskWindow() {
  console.log("Today Task Window");

  if (todayTaskWin && !todayTaskWin.isDestroyed()) {
    todayTaskWin.show();
    return;
  }

  todayTaskWin = new BrowserWindow({
    width: 500,
    height: 500,
    frame: false, // 🔒 removes the title bar and window controls
    resizable: false,
    webPreferences: {
      preload: path.join(__dirname, "./preload_files/today-task.js"),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });
  todayTaskWin.loadFile("./html_files/today-task.html");
}

function createLateCheckInWindow() {
  console.log("Late Check In Window");

  if (lateCheckInWin && !lateCheckInWin.isDestroyed()) {
    lateCheckInWin.show();
    return;
  }

  lateCheckInWin = new BrowserWindow({
    width: 500,
    height: 500,
    frame: false, // 🔒 removes the title bar and window controls
    resizable: false,
    webPreferences: {
      preload: path.join(__dirname, "./preload_files/late-checkin.js"),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });
  lateCheckInWin.loadFile("./html_files/late-checkin.html");
}

function createEarlyCheckOutWindow() {
  console.log("Early Check Out Window");

  if (earlyCheckOutWin && !earlyCheckOutWin.isDestroyed()) {
    earlyCheckOutWin.show();
    return;
  }

  earlyCheckOutWin = new BrowserWindow({
    width: 500,
    height: 500,
    frame: false, // 🔒 removes the title bar and window controls
    resizable: false,
    webPreferences: {
      preload: path.join(__dirname, "./preload_files/early-checkout.js"),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });
  earlyCheckOutWin.loadFile("./html_files/early-checkout.html");
}

function createOverTimeBreakWindow() {
  console.log("Over Time Break Window");

  if (overTimeBreakWin && !overTimeBreakWin.isDestroyed()) {
    overTimeBreakWin.show();
    return;
  }

  overTimeBreakWin = new BrowserWindow({
    width: 500,
    height: 500,
    frame: false, // 🔒 removes the title bar and window controls
    resizable: false,
    webPreferences: {
      preload: path.join(__dirname, "./preload_files/overtime-break.js"),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });
  overTimeBreakWin.loadFile("./html_files/overtime-break.html");
}

function createOtherBreakWindow() {
  console.log("Other Break Window");

  if (otherBreakWin && !otherBreakWin.isDestroyed()) {
    otherBreakWin.show();
    return;
  }

  otherBreakWin = new BrowserWindow({
    width: 500,
    height: 500,
    frame: false, // 🔒 removes the title bar and window controls
    resizable: false,
    webPreferences: {
      preload: path.join(__dirname, "./preload_files/other-break.js"),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });
  otherBreakWin.loadFile("./html_files/other-break.html");
}

function createComepletedTaskWindow() {
  console.log("Completed Task Window");

  if (completedTaskWin && !completedTaskWin.isDestroyed()) {
    completedTaskWin.show();
    return;
  }

  completedTaskWin = new BrowserWindow({
    width: 500,
    height: 500,
    frame: false, // 🔒 removes the title bar and window controls
    resizable: false,
    webPreferences: {
      preload: path.join(__dirname, "./preload_files/completed-task.js"),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });
  completedTaskWin.loadFile("./html_files/completed-task.html");
}

async function breakCreationFun(type, data) {
  // Creating break and storing at attendence collection
  try {
    let result = await axios.patch(
      `${process.env.API_URL}/attendance/lunchcreate/${
        store.get("attendence")._id
      }`,
      {
        id: store.get("attendence")._id,
        lunchIn: new Date().setMilliseconds(0),
        user: store.get("user")._id,
      },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );
  } catch (e) {
    console.log("Error ", e);
  }

  try {
    let result = await axios.patch(
      `${process.env.API_URL}/today/breakIn`,
      {
        type: type,
        description: data, // you must send this from renderer
        _id: store.get("todayActivity")._id,
      },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (result.status === 200) {
      // socket.emit('break-in', { status: 'break-in' });
      dashboardWin.webContents.send("break-in-emit",{type:type})
      store.set("todayActivity", result.data);
      // Tray icon setup
      const iconPath = path.join(__dirname, "./assets/meraki-logo-cross.png");
      const resizedIconBuffer = await sharp(iconPath)
        .resize(24, 24)
        .png()
        .toBuffer();
      const trayImage = nativeImage.createFromBuffer(resizedIconBuffer);
      tray.setImage(trayImage);


      const contextMenu = dynamicMenu(true, false);

      tray.setToolTip("My Meraki");
      tray.setContextMenu(contextMenu);
    }
  } catch (e) {
    console.log(" Errror ", e);
  }
}

async function logoutControllerFun() {
  if (isLoggingOut) return;
  isLoggingOut = true;
  stopTracking = true;

  const todayActivity = store.get("todayActivity");
  const attendance = store.get("attendence");
  const user = store.get("user");
  const authToken = store.get("authToken");

  if(!todayActivity?.checkOutTime && attendance?._id && user?._id) {

    try {
      let result = await axios.patch(
        `${process.env.API_URL}/attendance/${attendance._id}`,
        {
          user: user._id,
          checkOut: new Date(),
        },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
    } catch (e) {
      console.log("Error ", e);
    }
  
    // to enter check out time in database and update the status of checkout in activity collection
    try {
      let result = await axios.patch(
        `${process.env.API_URL}/today/checkout`,
        {
          _id: todayActivity._id,
        },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        }
      );
      // Immediately notify web of checkout
      if (dashboardWin && !dashboardWin.isDestroyed()) {
        dashboardWin.webContents.send("check-out-emit");
      }
    } catch (e) {
      console.log(" Errror ", e);
    }
  }
  // to store data into attendence collection

  // to get update activity of today
  if (!user?._id) {
    console.log("No user found, skipping logout");
    return;
  }
  
  let date = new Date();
  let id = user._id;
  console.log(" User That i got ", id);

  try {
    let result = await axios.get(
      `${process.env.API_URL}/today/history/${date}/${id}`,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      }
    );

    console.log("Today activity Result i got after logout ", result.data);
    if (result.data !== null && result.data !== undefined) {
      if (result.data.earlyCheckOutStatus && !result.data.earlyCheckOutDiscription ) {
        createEarlyCheckOutWindow();
        isLoggingOut = false; // Reset flag to allow popup interaction
        return; // Don't destroy tray yet, wait for user input
      } else if(!result.data.workStatus && !result.data.checkOutTime){
        createComepletedTaskWindow();
        isLoggingOut = false; // Reset flag to allow popup interaction
        return; // Don't destroy tray yet, wait for user input
      } else {
        store.clear()
      }
    }
    isLoggingOut = false; // Reset flag
  } catch (e) {}
  // Destroy tray if it exists
  if (tray && !tray.isDestroyed()) {
    tray.destroy();
    tray = null; // prevent reuse
  }
}

async function crashedLogoutControllerFun() {
  stopTracking = true;
  if (store.get("user") && store.get("todayActivity")) {
    // to store data into attendence collection
    try {
      console.log(" Logout attendancee for crashed ", store.get("attendence"));
      let result = await axios.patch(
        `${process.env.API_URL}/attendance/${store.get("attendence")._id}`,
        {
          user: store.get("user")._id,
          checkOut: new Date(),
        },
        {
          headers: {
            Authorization: `Bearer ${store.get("authToken")}`,
          },
        }
      );

      store.set("attendence", result.data);
    } catch (e) {
      console.log("Error ", e);
    }

    // to enter check out time in database and update the status of checkout in activity collection
    try {
      let result = await axios.patch(
        `${process.env.API_URL}/today/checkout`,
        {
          _id: store.get("todayActivity")._id,
        },
        {
          headers: {
            Authorization: `Bearer ${store.get("authToken")}`,
            "Content-Type": "application/json",
          },
        }
      );
      // console.log("Logout status ",result)
    } catch (e) {
      console.log(" Errror ", e);
    }
  }
  // Destroy tray if it exists
  if (tray && !tray.isDestroyed()) {
    tray.destroy();
    tray = null; // prevent reuse
  }
}

async function crashedBreakCreationFun(type, data) {
  // Creating break and storing at attendence collection

  if (store.get("attendence") && store.get("todayActivity")) {
    try {
      let result = await axios.patch(
        `${process.env.API_URL}/attendance/lunchcreate/${
          store.get("attendence")._id
        }`,
        {
          id: store.get("attendence")._id,
          lunchIn: new Date().setMilliseconds(0),
          user: store.get("user")._id,
        },
        {
          headers: {
            Authorization: `Bearer ${store.get("authToken")}`,
            "Content-Type": "application/json",
          },
        }
      );
    } catch (e) {
      console.log("Error ", e);
    }

    try {
      let result = await axios.patch(
        `${process.env.API_URL}/today/breakIn`,
        {
          type: type,
          description: data, // you must send this from renderer
          _id: store.get("todayActivity")._id,
        },
        {
          headers: {
            Authorization: `Bearer ${store.get("authToken")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (result.status === 200) {
        store.set("todayActivity", result.data);
      }
    } catch (e) {
      console.log(" Errror ", e);
    }
  }
}

function dynamicMenu(isLoggedIn, breakController) {
  return Menu.buildFromTemplate([
    {
      label: "My Dashboard",
      click: () => {
        if (store.get("authToken")) {
          createDashboardWindow();
        } else {
          createLoginWindow();
        }
      },
    },
    {
      label: "About PMS",
      click: () => {
        if (aboutWin && !aboutWin.isDestroyed()) {
          aboutWin.focus();
        } else {
          aboutWin = new BrowserWindow({
            width: 400,
            height: 410,
            webPreferences: {
              preload: path.join(__dirname, "./preload_files/about.js"),
              contextIsolation: true,
              nodeIntegration: false,
            },
          });
          aboutWin.loadFile("./html_files/about.html");
        }
      },
    },
    {
      label: "Check for Update",
      click: () => {
        if (updateWin && !updateWin.isDestroyed()) {
          updateWin.focus();
        } else {
          updateWin = new BrowserWindow({
            width: 400,
            height: 420,
            webPreferences: {
              preload: path.join(__dirname, "./preload_files/update.js"),
              contextIsolation: true,
              nodeIntegration: false,
            },
          });
          updateWin.loadFile("./html_files/update.html");
        }
      },
    },
    {
      label: "Go Online",
      enabled: isLoggedIn,
      click: async () => {
        if (
          store.get("todayActivity") &&
          store.get("todayActivity").breakStatus
        ) {
          // Updating break and storing at attendence collection
          try {
            let result = axios.patch(
              `${process.env.API_URL}/attendance/lunchupdate/${
                store.get("attendence")._id
              }`,
              {
                id: store.get("attendence")._id,
                lunchOut: new Date().setMilliseconds(0),
                user: store.get("user")._id,
              },
              {
                headers: {
                  Authorization: `Bearer ${store.get("authToken")}`,
                  "Content-Type": "application/json",
                },
              }
            );
          } catch (e) {
            console.log("Error ", e);
          }

          try {
            let result = await axios.patch(
              `${process.env.API_URL}/today/breakOut`,
              {
                _id: store.get("todayActivity")._id,
              },
              {
                headers: {
                  Authorization: `Bearer ${store.get("authToken")}`,
                  "Content-Type": "application/json",
                },
              }
            );

            store.set("todayActivity", result.data);
            const iconPath = path.join(__dirname, "./assets/meraki-logo.png");
            const resizedIconBuffer = await sharp(iconPath)
              .resize(24, 24)
              .png()
              .toBuffer();
            const trayImage = nativeImage.createFromBuffer(resizedIconBuffer);
            tray.setImage(trayImage);
            const contextMenu = dynamicMenu(false, true);
            tray.setToolTip("My Meraki");
            tray.setContextMenu(contextMenu);
            console.log("After Lunch Break : ", result.data);
            if (result.data.overLimitBreakStatus) {
              createOverTimeBreakWindow();
            }
            // socket.emit('break-out', { status: 'break-out' });
            dashboardWin.webContents.send("break-out-emit",{type:"breakOut"})
          } catch (e) {
            console.log("Error ", e);
          }
        }
      },
    },
    {
      label: "Go Offline",
      enabled: breakController,
      submenu: [
        {
          label: "Lunch Break",
          click: async () => {
            breakCreationFun("lunchBreak", "Lunch Break");
          },
        },
        {
          label: "Tea Break",
          click: async () => {
            breakCreationFun("teaBreak", "Tea Break");
          },
        },
        {
          label: "Other",
          click: () => {
            createOtherBreakWindow();
          },
        },
      ],
    },
    { type: "separator" },
    {
      label: "Logout",
      click: async () => {
        // Close all open windows
        BrowserWindow.getAllWindows().forEach((win) => {
          if (!win.isDestroyed()) win.close();
        });
        logoutControllerFun();
      },
    },
    {
      label: "Quit PMS",
      click: () => {
        store.clear();
        app.quit();
      },
    },
  ]);
}

function isNewDay() {
  const today = new Date().toISOString().slice(0, 10); // e.g. "2025-05-13"
  const lastClearedDate = store.get("lastClearedDate");
  return lastClearedDate !== today;
}

function clearDailyData() {
  if (isNewDay()) {
    console.log("New day detected. Clearing local data...");

    // Update last cleared date
    const today = new Date().toISOString().slice(0, 10);
    store.set("lastClearedDate", today);
  } else {
    console.log("Same day, data remains.");
  }
}

async function getTaskDataFun() {

  if(!store.get("user")) {
    return
  }
  try {
    let productResult = await axios.get(
      `${process.env.API_URL}/product/user/${store.get("user")._id}`,
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );
    products = productResult.data.data;

    const userID = store.get("user")._id;
    const todayActivity = store.get("todayActivity");
    const authToken = store.get("authToken");
    console.log(" Updated Products main js ");
    dashboardWin.webContents.send("get-product", {
      products,
      userID,
      todayActivity,
      authToken,
    });
  } catch (e) {
    console.log("Error ", e);
  }
}

// User's input detection function
async function resetIdleTimer() {
  if (idleTimeout) clearTimeout(idleTimeout);

  if (store.get("todayActivity")?.breakStatus) return;

  if (isIdle) {
    // User is active again
    isIdle = false;

    // Call idle end API
    await axios.patch(
      `${process.env.API_URL}/today/idelend`,
      {
        _id: store.get("todayActivity")._id,
      },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );
    const iconPath = path.join(__dirname, "./assets/meraki-logo.png");
    const resizedIconBuffer = await sharp(iconPath)
      .resize(24, 24)
      .png()
      .toBuffer();
    const trayImage = nativeImage.createFromBuffer(resizedIconBuffer);
    tray.setImage(trayImage);

    const contextMenu = dynamicMenu(false, true);

    tray.setToolTip("My Meraki");
    tray.setContextMenu(contextMenu);
  }

  idleTimeout = setTimeout(async () => {
    

    if (store.get("todayActivity")?.breakStatus) return;
    isIdle = true;
    // Call idle start API
    await axios.patch(
      `${process.env.API_URL}/today/idelstart`,
      {
        _id: store.get("todayActivity")._id,
      },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );
    const iconPath = path.join(__dirname, "./assets/meraki-logo-idle.png");
    const resizedIconBuffer = await sharp(iconPath)
      .resize(24, 24)
      .png()
      .toBuffer();
    const trayImage = nativeImage.createFromBuffer(resizedIconBuffer);
    tray.setImage(trayImage);

    const contextMenu = dynamicMenu(true, false);

    tray.setToolTip("My Meraki");
    tray.setContextMenu(contextMenu);
  }, IDLE_LIMIT);
}

// Tracking Function
async function startProductivityTracking() {
  stopTracking = false;

  console.log("Start productivityu tracking ")
  async function track() {
    
    if (stopTracking) return;
    if (
      store.get("authToken") &&
      store.get("todayActivity") &&
      !store.get("todayActivity").checkOutTime && !store.get("todayActivity").breakStatus
    ) {
      console.log("Un");
      try {
        const headers = {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        };

        const payload = {
          _id: store.get("todayActivity")._id,
          user: store.get("user")._id,
        };

        if (!store.get("todayActivity")?.breakStatus) {
          payload.totalSlot = 1;
          payload.filledSlot = isIdle ? 0 : 1;
        } 

        const result = await axios.patch(
          `${process.env.API_URL}/today/product`,
          payload,
          { headers }
        );

        try {
          let date = new Date();
          let id = store.get("user")._id;
          let result = await axios.get(
            `${process.env.API_URL}/today/history/${date}/${id}`,
            {
              headers: {
                Authorization: `Bearer ${store.get("authToken")}`,
              },
            }
          );

          if (result.data !== null || result.data !== undefined) {
            store.set("todayActivity", result.data);
          } 
        } catch (e) {
          console.log(" error ", e);
        }
      } catch (err) {
        console.error("Productivity update failed:", err.message);
      }
      const todayActivity = store.get("todayActivity")
      dashboardWin.webContents.send("update-progress",{todayActivity});
    }

    // Wait 1 minute before next run
    setTimeout(track, 60000);
  }

  track(); // start the first cycle
}



// App startup
app.whenReady().then(async () => {
  store.clear();

  clearDailyData();
  if (store.get("authToken")) {
    await createDashboardWindow();
  } else {
    createLoginWindow();
  }
  
});

// Activities Handler
ipcMain.on("today-goal", async (event, data) => {
  console.log("Desktop todaya goals",data)
  try {
    const result = await axios.post(
      `${process.env.API_URL}/today/goal`,
      {
        todaysGoal: data, // you must send this from renderer
      },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );
    store.set("todayActivity", result.data);
    if (result.data.lateCheckInStatus) {
      createLateCheckInWindow();
    }
    if (store.get("todayActivity")) {
      getTaskDataFun();
    }
  } catch (err) {
    console.log();
  }
    // socket.emit('check-in', { status: 'checked-in' });
    dashboardWin.webContents.send("check-in-emit")
  todayTaskWin.close();
});

ipcMain.on("late-checkin", async (event, data) => {
  console.log(" Data Got for late check in ", data);
  try {
    let result = await axios.patch(
      `${process.env.API_URL}/today/late/checkin`,
      {
        description: data, // you must send this from renderer
        _id: store.get("todayActivity")._id,
      },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );
    // console.log("Late Check In Status ",result)
    if (result.status === 200) {
      // Send the late check-in data to dashboard
      dashboardWin.webContents.send("late-checkin-emit", data);
      lateCheckInWin.close();
    }
  } catch (e) {
    console.log(" Errror ", e);
  }
});

ipcMain.on("completed-task", async (event, data) => {
  console.log("Completed Task  ", data);
  const todayActivity = store.get("todayActivity");
  if (!todayActivity?._id) {
    console.log("No today activity found");
    return;
  }
  try {
    let result = await axios.patch(
      `${process.env.API_URL}/today/status`,
      {
        reasone: data, // you must send this from renderer
        _id: todayActivity._id,
      },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (result.status === 200) {
      completedTaskWin.close();
      store.clear();
      // Destroy tray and quit app after completing the flow
      if (tray && !tray.isDestroyed()) {
        tray.destroy();
        tray = null;
      }
      app.quit();
    }
  } catch (e) {
    console.log(" Errror ", e);
  }
});

ipcMain.on("early-checkout", async (event, data) => {
  try {
    let result = await axios.patch(
      `${process.env.API_URL}/today/early/checkout`,
      {
        description: data, // you must send this from renderer
        _id: store.get("todayActivity")._id,
      },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (result.status === 200) {
      earlyCheckOutWin.close();
      createComepletedTaskWindow();
    }
  } catch (e) {
    console.log(" Errror ", e);
  }
});

ipcMain.on("other-break", async (event, data) => {
  try {
    let result = await axios.patch(
      `${process.env.API_URL}/today/breakIn`,
      {
        type: data.type,
        description: data.description, // you must send this from renderer
        _id: store.get("todayActivity")._id,
      },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (result.status === 200) {
      otherBreakWin.close();
      dashboardWin.webContents.send("break-in-emit",{type:"other"})

      store.set("todayActivity", result.data);
      // Tray icon setup
      const iconPath = path.join(__dirname, "./assets/meraki-logo-cross.png");
      const resizedIconBuffer = await sharp(iconPath)
        .resize(24, 24)
        .png()
        .toBuffer();
      const trayImage = nativeImage.createFromBuffer(resizedIconBuffer);
      tray.setImage(trayImage);

      const contextMenu = dynamicMenu(true, false);

      tray.setToolTip("My Meraki");
      tray.setContextMenu(contextMenu);
    }
  } catch (e) {
    console.log(" Errror ", e);
  }
});

ipcMain.on("overtime-break", async (event, data) => {
  try {
    let result = await axios.patch(
      `${process.env.API_URL}/today/break/over`,
      {
        description: data, // you must send this from renderer
        _id: store.get("todayActivity")._id,
      },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("Result After Overlimit Break Submit ", result.data);

    if (result.status === 200) {
      overTimeBreakWin.close();

      store.set("todayActivity", result.data);
    }
  } catch (e) {
    console.log(" Errror ", e);
  }
});

// Task Getting 
ipcMain.on("get-recent-tasks",async (event, data) => {
  getTaskDataFun()
})

// Task Operations
ipcMain.handle("start-task", async (event, data) => {
  try {
    const { taskId, projectId, date } = data;
    console.log("Starting task with data:", { taskId, projectId, date });
    const result = await axios.patch(
      `${process.env.API_URL}/product/start-task/${projectId}/${taskId}`,
      { date },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );
    return result.data;
  } catch (error) {
    console.error("Start task API error:", error.response?.data || error.message);
    throw error;
  }
});

ipcMain.handle("pause-task", async (event, data) => {
  try {
    const { taskId, projectId, elapsedTime, pauseTime, date, startTime } = data;
    const result = await axios.patch(
      `${process.env.API_URL}/product/pause-task/${projectId}/${taskId}`,
      { elapsedTime, pauseTime, date, startTime },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );
    return result.data;
  } catch (error) {
    console.error("Pause task API error:", error);
    throw error;
  }
});

ipcMain.handle("stop-task", async (event, data) => {
  try {
    const { taskId, projectId, elapsedTime, date } = data;
    const result = await axios.patch(
      `${process.env.API_URL}/product/stop-task/${projectId}/${taskId}`,
      { elapsedTime, date },
      {
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );
    return result.data;
  } catch (error) {
    console.error("Stop task API error:", error);
    throw error;
  }
});

// Login IPC handler
ipcMain.on("login-success", async (event, token) => {
  // to get user's profile
  console.log("login sucess called",process.env.API_URL)
  try {
    store.set("authToken", token);

    if (loginWin && !loginWin.isDestroyed()) loginWin.close();

    // Get user profile
    const result = await axios.get(`${process.env.API_URL}/user/profile`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    store.set("user", result.data);

    // Open dashboard
    createDashboardWindow();
    // dashboardWin.webContents.send("socket-init",{token})
    // connectSocket(token)
  } catch (error) {
    console.error("Login processing error:", error.message);
  }

  console.log("to check user created today attendence or not");
  // to check user created today attendence or not
  try {
    let todayAttendance = await axios.get(
      `${process.env.API_URL}/attendance/today`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
   
    console.log("Current today attendance ",todayAttendance.data)
    // to create attendancd
    if (
      todayAttendance.data === null ||
      todayAttendance.data === undefined ||
      todayAttendance.data === ""
    ) {
      console.log("Today Attendance 1 ");

      if(store.get("user")) {

        try {
          let result = await axios.post(
            `${process.env.API_URL}/attendance`,
            {
              user: store.get("user")._id,
              checkIn: new Date(),
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );
          console.log(" Today Attendacne ", result.data);
          store.set("attendence", result.data);
        } catch (e) {
          console.log("Error ", e);
        }
      }
    } else {
      console.log("Today Attendance 2 ");

      if(todayAttendance.data.checkOut) {

        try {
          let resultAttendance = await axios.patch(
            `${process.env.API_URL}/attendance/checkOut/delete`,
            {
              id: todayAttendance.data._id,
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );
          store.set("attendence", resultAttendance.data);
         
          
        } catch (error) {
          // Handle errors
          console.error("Error updating attendance:", error);
        }
          dashboardWin.webContents.send("check-in-emit")
      }
    }
  } catch (e) {
    console.log("TOday Error ", e);
  }

  // To get today's history
  if(store.get("user")) {

    try {
      let date = new Date();
      let id = store.get("user")._id;
      let result = await axios.get(
        `${process.env.API_URL}/today/history/${date}/${id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
  
      if (result.data !== null || result.data !== undefined) {
        if (result?.data?.checkOutTime) {
          let resultActivity = await axios.patch(
            `${process.env.API_URL}/today/checkOut/delete`,
            {
              id: result.data._id,
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );
  
          store.set("todayActivity", resultActivity.data);
     
        } else {
          
          store.set("todayActivity", result.data);
        }
      } else {
        store.set("todayActivity", result.data);
      }
    } catch (e) {
      console.log(" error ", e);
    }
  }
  console.log(" To get today's history : ", store.get("todayActivity"));

  scheduleNextCapture();
  createLoginSuccessWin();
  startProductivityTracking();
  dashboardWin.webContents.send("clear-task");
});

// socket handler for hrms activities 
ipcMain.on("socket-breakin", async (event,data) => {
     let date = new Date();
          let id = store.get("user")._id;
          let result = await axios.get(
            `${process.env.API_URL}/today/history/${date}/${id}`,
            {
              headers: {
                Authorization: `Bearer ${store.get("authToken")}`,
              },
            }
          );

          if (result.data !== null || result.data !== undefined) {
            store.set("todayActivity", result.data);
          } 
      
      // Tray icon setup
      const iconPath = path.join(__dirname, "./assets/meraki-logo-cross.png");
      const resizedIconBuffer = await sharp(iconPath)
        .resize(24, 24)
        .png()
        .toBuffer();
      const trayImage = nativeImage.createFromBuffer(resizedIconBuffer);
      tray.setImage(trayImage);


      const contextMenu = dynamicMenu(true, false);

      tray.setToolTip("My Meraki");
      tray.setContextMenu(contextMenu);
  
})

ipcMain.on("socket-breakout",async(event,data) => {
    let date = new Date();
          let id = store.get("user")._id;
          let result = await axios.get(
            `${process.env.API_URL}/today/history/${date}/${id}`,
            {
              headers: {
                Authorization: `Bearer ${store.get("authToken")}`,
              },
            }
          );

          if (result.data !== null || result.data !== undefined) {
            store.set("todayActivity", result.data);
          } 

      const iconPath = path.join(__dirname, "./assets/meraki-logo.png");
            const resizedIconBuffer = await sharp(iconPath)
              .resize(24, 24)
              .png()
              .toBuffer();
            const trayImage = nativeImage.createFromBuffer(resizedIconBuffer);
            tray.setImage(trayImage);
            const contextMenu = dynamicMenu(false, true);
            tray.setToolTip("My Meraki");
            tray.setContextMenu(contextMenu);
})

ipcMain.on("socket-checkout",async(event,data) => {
  app.quit()
})

// macOS dock click
app.on("activate", () => {
  if (store.get("authToken")) {
    createDashboardWindow();
  } else {
    createLoginWindow();
  }
});

// In your `window-all-closed` handler, log open windows
app.on("window-all-closed", () => {
  console.log("Remaining windows:", BrowserWindow.getAllWindows().length);
  // if (process.platform !== "darwin") {
  //   app.quit();
  // }
});

// To get recent updated tasks
ipcMain.on("get-updated-tasks", (event) => {
  console.log("Getting updated tasks");
  getTaskDataFun();
});

// Handle "before-quit" safely
app.on("before-quit", async (event) => {
  if (isForceQuitting) return;

  event.preventDefault();

  stopScreenCapture();
  // Your async cleanup
  await logoutControllerFun();

  // Only force quit if no popups are shown
  if (!earlyCheckOutWin && !completedTaskWin) {
    isForceQuitting = true;
    app.exit(0);
  }
});

ipcMain.handle("store:get", (event, key) => {
  return store.get(key);
});

ipcMain.handle("store:set", (event, key, value) => {
  store.set(key, value);
});

let captureTimeout = null; // Store timeout ID for cleanup

async function captureScreen() {
  try {
    const displays = screen.getAllDisplays();
    const primaryDisplay = displays.find(
      (d) => d.id === screen.getPrimaryDisplay().id
    );

    const sources = await desktopCapturer.getSources({
      types: ["screen"],
      thumbnailSize: primaryDisplay.size,
    });

    const screenshot = sources.find(
      (s) => s.display_id === primaryDisplay.id.toString()
    );
    if (!screenshot?.thumbnail) throw new Error("Capture failed");

    // 2. Save to Temp File (Electron-safe path)
    const tempDir = app.getPath("temp");
    const tempFile = path.join(tempDir, `screenshot-${Date.now()}.png`);
    fs.writeFileSync(tempFile, screenshot.thumbnail.toPNG());

    // 3. Prepare Upload (Electron-compatible approach)
    const form = new FormData();
    form.append("screenshot", fs.createReadStream(tempFile), {
      filename: path.basename(tempFile),
      contentType: "image/png",
    });
    form.append("keyCounts", keyCounts);

    try {
      const response = await axios({
        method: "post",
        url: `${process.env.API_URL}/track/upload-screenshot`,
        data: form,
        headers: {
          Authorization: `Bearer ${store.get("authToken")}`,
        },
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
      });
      console.log("✅ Uploaded:", response.data);
    } catch (error) {
      console.error("❌ Upload failed:", error.message);
    }
    keyCounts = 0;
    // 5. Cleanup
    fs.unlink(tempFile, () => {});
  } catch (error) {
    console.error("❌ Failed to capture screen:", error);
  } finally {
    scheduleNextCapture(); // Schedule next capture after current one finishes
  }
}

function scheduleNextCapture() {
  const MIN_DELAY = 5 * 60 * 1000; // 5 minutes (minimum)
  const MAX_DELAY = 10 * 60 * 1000; // 10 minutes (maximum)

  const randomDelay = Math.floor(
    Math.random() * (MAX_DELAY - MIN_DELAY + 1) + MIN_DELAY
  );

  console.log(`⏳ Next capture in ${randomDelay / 1000 / 60} minutes`);

  captureTimeout = setTimeout(captureScreen, randomDelay);
}

// Stop capturing (call this when app closes)
function stopScreenCapture() {
  if (captureTimeout) {
    clearTimeout(captureTimeout);
    captureTimeout = null;
  }
}

// User's input listener
gkl.addListener(function (e) {
  if (e.state === "DOWN") {
    keyCounts++;
    if (!store.get("todayActivity")?.breakStatus) {
      resetIdleTimer();
    }
  }
});



module.exports = store;
